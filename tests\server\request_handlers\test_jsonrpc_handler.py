import unittest
import unittest.async_case

from collections.abc import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Any, <PERSON>R<PERSON><PERSON>
from unittest.mock import AsyncMock, MagicMock, call, patch

import httpx
import pytest

from a2a.server.agent_execution import AgentExecutor, RequestContext
from a2a.server.agent_execution.request_context_builder import (
    RequestContextBuilder,
)
from a2a.server.context import Server<PERSON>allContext
from a2a.server.events import QueueManager
from a2a.server.events.event_queue import EventQueue
from a2a.server.request_handlers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JSONRPCHandler
from a2a.server.tasks import InMemoryPushNotifier, PushNotifier, TaskStore
from a2a.types import (
    AgentC<PERSON>bilities,
    AgentCard,
    Artifact,
    CancelTaskRequest,
    CancelTaskSuccessResponse,
    GetTaskPushNotificationConfigRequest,
    GetTaskPushNotificationConfigResponse,
    GetTaskPushNotificationConfigSuccessResponse,
    GetTaskRequest,
    GetTaskResponse,
    GetTaskSuccessResponse,
    InternalError,
    JSONRPCErrorResponse,
    Message,
    MessageSendConfiguration,
    MessageSendParams,
    Part,
    PushNotificationConfig,
    SendMessageRequest,
    SendMessageSuccessResponse,
    SendStreamingMessageRequest,
    SendStreamingMessageSuccessResponse,
    SetTaskPushNotificationConfigRequest,
    SetTaskPushNotificationConfigResponse,
    SetTaskPushNotificationConfigSuccessResponse,
    Task,
    TaskArtifactUpdateEvent,
    TaskIdParams,
    TaskNotFoundError,
    TaskPushNotificationConfig,
    TaskQueryParams,
    TaskResubscriptionRequest,
    TaskState,
    TaskStatus,
    TaskStatusUpdateEvent,
    TextPart,
    UnsupportedOperationError,
)
from a2a.utils.errors import ServerError


MINIMAL_TASK: dict[str, Any] = {
    'id': 'task_123',
    'contextId': 'session-xyz',
    'status': {'state': 'submitted'},
    'kind': 'task',
}
MESSAGE_PAYLOAD: dict[str, Any] = {
    'role': 'agent',
    'parts': [{'text': 'test message'}],
    'messageId': '111',
}


class TestJSONRPCtHandler(unittest.async_case.IsolatedAsyncioTestCase):
    @pytest.fixture(autouse=True)
    def init_fixtures(self) -> None:
        self.mock_agent_card = MagicMock(
            spec=AgentCard, url='http://agent.example.com/api'
        )

    async def test_on_get_task_success(self) -> None:
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )
        call_context = ServerCallContext(state={'foo': 'bar'})
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)
        task_id = 'test_task_id'
        mock_task = Task(**MINIMAL_TASK)
        mock_task_store.get.return_value = mock_task
        request = GetTaskRequest(id='1', params=TaskQueryParams(id=task_id))
        response: GetTaskResponse = await handler.on_get_task(
            request, call_context
        )
        self.assertIsInstance(response.root, GetTaskSuccessResponse)
        assert response.root.result == mock_task  # type: ignore
        mock_task_store.get.assert_called_once_with(task_id)

    async def test_on_get_task_not_found(self) -> None:
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)
        mock_task_store.get.return_value = None
        request = GetTaskRequest(
            id='1',
            method='tasks/get',
            params=TaskQueryParams(id='nonexistent_id'),
        )
        call_context = ServerCallContext(state={'foo': 'bar'})
        response: GetTaskResponse = await handler.on_get_task(
            request, call_context
        )
        self.assertIsInstance(response.root, JSONRPCErrorResponse)
        assert response.root.error == TaskNotFoundError()  # type: ignore

    async def test_on_cancel_task_success(self) -> None:
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)
        task_id = 'test_task_id'
        mock_task = Task(**MINIMAL_TASK)
        mock_task_store.get.return_value = mock_task
        mock_agent_executor.cancel.return_value = None
        call_context = ServerCallContext(state={'foo': 'bar'})

        async def streaming_coro():
            yield mock_task

        with patch(
            'a2a.server.request_handlers.default_request_handler.EventConsumer.consume_all',
            return_value=streaming_coro(),
        ):
            request = CancelTaskRequest(id='1', params=TaskIdParams(id=task_id))
            response = await handler.on_cancel_task(request, call_context)
            assert mock_agent_executor.cancel.call_count == 1
            self.assertIsInstance(response.root, CancelTaskSuccessResponse)
            assert response.root.result == mock_task  # type: ignore
            mock_agent_executor.cancel.assert_called_once()

    async def test_on_cancel_task_not_supported(self) -> None:
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)
        task_id = 'test_task_id'
        mock_task = Task(**MINIMAL_TASK)
        mock_task_store.get.return_value = mock_task
        mock_agent_executor.cancel.return_value = None
        call_context = ServerCallContext(state={'foo': 'bar'})

        async def streaming_coro():
            raise ServerError(UnsupportedOperationError())
            yield

        with patch(
            'a2a.server.request_handlers.default_request_handler.EventConsumer.consume_all',
            return_value=streaming_coro(),
        ):
            request = CancelTaskRequest(id='1', params=TaskIdParams(id=task_id))
            response = await handler.on_cancel_task(request, call_context)
            assert mock_agent_executor.cancel.call_count == 1
            self.assertIsInstance(response.root, JSONRPCErrorResponse)
            assert response.root.error == UnsupportedOperationError()  # type: ignore
            mock_agent_executor.cancel.assert_called_once()

    async def test_on_cancel_task_not_found(self) -> None:
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)
        mock_task_store.get.return_value = None
        request = CancelTaskRequest(
            id='1',
            method='tasks/cancel',
            params=TaskIdParams(id='nonexistent_id'),
        )
        response = await handler.on_cancel_task(request)
        self.assertIsInstance(response.root, JSONRPCErrorResponse)
        assert response.root.error == TaskNotFoundError()  # type: ignore
        mock_task_store.get.assert_called_once_with('nonexistent_id')
        mock_agent_executor.cancel.assert_not_called()

    @patch(
        'a2a.server.agent_execution.simple_request_context_builder.SimpleRequestContextBuilder.build'
    )
    async def test_on_message_new_message_success(
        self, _mock_builder_build: AsyncMock
    ) -> None:
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)
        mock_task = Task(**MINIMAL_TASK)
        mock_task_store.get.return_value = mock_task
        mock_agent_executor.execute.return_value = None

        _mock_builder_build.return_value = RequestContext(
            request=MagicMock(),
            task_id='task_123',
            context_id='session-xyz',
            task=None,
            related_tasks=None,
        )

        async def streaming_coro():
            yield mock_task

        with patch(
            'a2a.server.request_handlers.default_request_handler.EventConsumer.consume_all',
            return_value=streaming_coro(),
        ):
            request = SendMessageRequest(
                id='1',
                params=MessageSendParams(message=Message(**MESSAGE_PAYLOAD)),
            )
            response = await handler.on_message_send(request)
            assert mock_agent_executor.execute.call_count == 1
            self.assertIsInstance(response.root, SendMessageSuccessResponse)
            assert response.root.result == mock_task  # type: ignore
            mock_agent_executor.execute.assert_called_once()

    async def test_on_message_new_message_with_existing_task_success(
        self,
    ) -> None:
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)
        mock_task = Task(**MINIMAL_TASK)
        mock_task_store.get.return_value = mock_task
        mock_agent_executor.execute.return_value = None

        async def streaming_coro():
            yield mock_task

        with patch(
            'a2a.server.request_handlers.default_request_handler.EventConsumer.consume_all',
            return_value=streaming_coro(),
        ):
            request = SendMessageRequest(
                id='1',
                params=MessageSendParams(
                    message=Message(
                        **MESSAGE_PAYLOAD,
                        taskId=mock_task.id,
                        contextId=mock_task.contextId,
                    )
                ),
            )
            response = await handler.on_message_send(request)
            assert mock_agent_executor.execute.call_count == 1
            self.assertIsInstance(response.root, SendMessageSuccessResponse)
            assert response.root.result == mock_task  # type: ignore
            mock_agent_executor.execute.assert_called_once()

    async def test_on_message_error(self) -> None:
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)
        mock_task_store.get.return_value = None
        mock_agent_executor.execute.return_value = None

        async def streaming_coro():
            raise ServerError(error=UnsupportedOperationError())
            yield

        with patch(
            'a2a.server.request_handlers.default_request_handler.EventConsumer.consume_all',
            return_value=streaming_coro(),
        ):
            request = SendMessageRequest(
                id='1',
                params=MessageSendParams(
                    message=Message(
                        **MESSAGE_PAYLOAD,
                    )
                ),
            )
            response = await handler.on_message_send(request)

            self.assertIsInstance(response.root, JSONRPCErrorResponse)
            assert response.root.error == UnsupportedOperationError()  # type: ignore
            mock_agent_executor.execute.assert_called_once()

    @patch(
        'a2a.server.agent_execution.simple_request_context_builder.SimpleRequestContextBuilder.build'
    )
    async def test_on_message_stream_new_message_success(
        self, _mock_builder_build: AsyncMock
    ) -> None:
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )

        self.mock_agent_card.capabilities = AgentCapabilities(streaming=True)
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)
        _mock_builder_build.return_value = RequestContext(
            request=MagicMock(),
            task_id='task_123',
            context_id='session-xyz',
            task=None,
            related_tasks=None,
        )

        events: list[Any] = [
            Task(**MINIMAL_TASK),
            TaskArtifactUpdateEvent(
                taskId='task_123',
                contextId='session-xyz',
                artifact=Artifact(
                    artifactId='11', parts=[Part(TextPart(text='text'))]
                ),
            ),
            TaskStatusUpdateEvent(
                taskId='task_123',
                contextId='session-xyz',
                status=TaskStatus(state=TaskState.completed),
                final=True,
            ),
        ]

        async def streaming_coro():
            for event in events:
                yield event

        with patch(
            'a2a.server.request_handlers.default_request_handler.EventConsumer.consume_all',
            return_value=streaming_coro(),
        ):
            mock_task_store.get.return_value = None
            mock_agent_executor.execute.return_value = None
            request = SendStreamingMessageRequest(
                id='1',
                params=MessageSendParams(message=Message(**MESSAGE_PAYLOAD)),
            )
            response = handler.on_message_send_stream(request)
            assert isinstance(response, AsyncGenerator)
            collected_events: list[Any] = []
            async for event in response:
                collected_events.append(event)
            assert len(collected_events) == len(events)
            for i, event in enumerate(collected_events):
                assert isinstance(
                    event.root, SendStreamingMessageSuccessResponse
                )
                assert event.root.result == events[i]
            mock_agent_executor.execute.assert_called_once()

    async def test_on_message_stream_new_message_existing_task_success(
        self,
    ) -> None:
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )

        self.mock_agent_card.capabilities = AgentCapabilities(streaming=True)

        handler = JSONRPCHandler(self.mock_agent_card, request_handler)
        mock_task = Task(**MINIMAL_TASK, history=[])
        events: list[Any] = [
            mock_task,
            TaskArtifactUpdateEvent(
                taskId='task_123',
                contextId='session-xyz',
                artifact=Artifact(
                    artifactId='11', parts=[Part(TextPart(text='text'))]
                ),
            ),
            TaskStatusUpdateEvent(
                taskId='task_123',
                contextId='session-xyz',
                status=TaskStatus(state=TaskState.working),
                final=True,
            ),
        ]

        async def streaming_coro():
            for event in events:
                yield event

        with patch(
            'a2a.server.request_handlers.default_request_handler.EventConsumer.consume_all',
            return_value=streaming_coro(),
        ):
            mock_task_store.get.return_value = mock_task
            mock_agent_executor.execute.return_value = None
            request = SendStreamingMessageRequest(
                id='1',
                params=MessageSendParams(
                    message=Message(
                        **MESSAGE_PAYLOAD,
                        taskId=mock_task.id,
                        contextId=mock_task.contextId,
                    )
                ),
            )
            response = handler.on_message_send_stream(request)
            assert isinstance(response, AsyncGenerator)
            collected_events = [item async for item in response]
            assert len(collected_events) == len(events)
            mock_agent_executor.execute.assert_called_once()
            assert mock_task.history is not None and len(mock_task.history) == 1

    async def test_set_push_notification_success(self) -> None:
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        mock_push_notifier = AsyncMock(spec=PushNotifier)
        request_handler = DefaultRequestHandler(
            mock_agent_executor,
            mock_task_store,
            push_notifier=mock_push_notifier,
        )
        self.mock_agent_card.capabilities = AgentCapabilities(
            streaming=True, pushNotifications=True
        )
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)
        mock_task = Task(**MINIMAL_TASK)
        mock_task_store.get.return_value = mock_task
        task_push_config = TaskPushNotificationConfig(
            taskId=mock_task.id,
            pushNotificationConfig=PushNotificationConfig(
                url='http://example.com'
            ),
        )
        request = SetTaskPushNotificationConfigRequest(
            id='1', params=task_push_config
        )
        response: SetTaskPushNotificationConfigResponse = (
            await handler.set_push_notification(request)
        )
        self.assertIsInstance(
            response.root, SetTaskPushNotificationConfigSuccessResponse
        )
        assert response.root.result == task_push_config  # type: ignore
        mock_push_notifier.set_info.assert_called_once_with(
            mock_task.id, task_push_config.pushNotificationConfig
        )

    async def test_get_push_notification_success(self) -> None:
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        mock_httpx_client = AsyncMock(spec=httpx.AsyncClient)
        push_notifier = InMemoryPushNotifier(httpx_client=mock_httpx_client)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store, push_notifier=push_notifier
        )
        self.mock_agent_card.capabilities = AgentCapabilities(
            streaming=True, pushNotifications=True
        )
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)
        mock_task = Task(**MINIMAL_TASK)
        mock_task_store.get.return_value = mock_task
        task_push_config = TaskPushNotificationConfig(
            taskId=mock_task.id,
            pushNotificationConfig=PushNotificationConfig(
                url='http://example.com'
            ),
        )
        request = SetTaskPushNotificationConfigRequest(
            id='1', params=task_push_config
        )
        await handler.set_push_notification(request)

        get_request: GetTaskPushNotificationConfigRequest = (
            GetTaskPushNotificationConfigRequest(
                id='1', params=TaskIdParams(id=mock_task.id)
            )
        )
        get_response: GetTaskPushNotificationConfigResponse = (
            await handler.get_push_notification(get_request)
        )
        self.assertIsInstance(
            get_response.root, GetTaskPushNotificationConfigSuccessResponse
        )
        assert get_response.root.result == task_push_config  # type: ignore

    @patch(
        'a2a.server.agent_execution.simple_request_context_builder.SimpleRequestContextBuilder.build'
    )
    async def test_on_message_stream_new_message_send_push_notification_success(
        self, _mock_builder_build: AsyncMock
    ) -> None:
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        mock_httpx_client = AsyncMock(spec=httpx.AsyncClient)
        push_notifier = InMemoryPushNotifier(httpx_client=mock_httpx_client)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store, push_notifier=push_notifier
        )
        self.mock_agent_card.capabilities = AgentCapabilities(
            streaming=True, pushNotifications=True
        )
        _mock_builder_build.return_value = RequestContext(
            request=MagicMock(),
            task_id='task_123',
            context_id='session-xyz',
            task=None,
            related_tasks=None,
        )

        handler = JSONRPCHandler(self.mock_agent_card, request_handler)
        events: list[Any] = [
            Task(**MINIMAL_TASK),
            TaskArtifactUpdateEvent(
                taskId='task_123',
                contextId='session-xyz',
                artifact=Artifact(
                    artifactId='11', parts=[Part(TextPart(text='text'))]
                ),
            ),
            TaskStatusUpdateEvent(
                taskId='task_123',
                contextId='session-xyz',
                status=TaskStatus(state=TaskState.completed),
                final=True,
            ),
        ]

        async def streaming_coro():
            for event in events:
                yield event

        with patch(
            'a2a.server.request_handlers.default_request_handler.EventConsumer.consume_all',
            return_value=streaming_coro(),
        ):
            mock_task_store.get.return_value = None
            mock_agent_executor.execute.return_value = None
            mock_httpx_client.post.return_value = httpx.Response(200)
            request = SendStreamingMessageRequest(
                id='1',
                params=MessageSendParams(message=Message(**MESSAGE_PAYLOAD)),
            )
            request.params.configuration = MessageSendConfiguration(
                acceptedOutputModes=['text'],
                pushNotificationConfig=PushNotificationConfig(
                    url='http://example.com'
                ),
            )
            response = handler.on_message_send_stream(request)
            assert isinstance(response, AsyncGenerator)

            collected_events = [item async for item in response]
            assert len(collected_events) == len(events)

            calls = [
                call(
                    'http://example.com',
                    json={
                        'contextId': 'session-xyz',
                        'id': 'task_123',
                        'kind': 'task',
                        'status': {'state': 'submitted'},
                    },
                ),
                call(
                    'http://example.com',
                    json={
                        'artifacts': [
                            {
                                'artifactId': '11',
                                'parts': [
                                    {
                                        'kind': 'text',
                                        'text': 'text',
                                    }
                                ],
                            }
                        ],
                        'contextId': 'session-xyz',
                        'id': 'task_123',
                        'kind': 'task',
                        'status': {'state': 'submitted'},
                    },
                ),
                call(
                    'http://example.com',
                    json={
                        'artifacts': [
                            {
                                'artifactId': '11',
                                'parts': [
                                    {
                                        'kind': 'text',
                                        'text': 'text',
                                    }
                                ],
                            }
                        ],
                        'contextId': 'session-xyz',
                        'id': 'task_123',
                        'kind': 'task',
                        'status': {'state': 'completed'},
                    },
                ),
            ]
            mock_httpx_client.post.assert_has_calls(calls)

    async def test_on_resubscribe_existing_task_success(
        self,
    ) -> None:
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        mock_queue_manager = AsyncMock(spec=QueueManager)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store, mock_queue_manager
        )
        self.mock_agent_card = MagicMock(spec=AgentCard)
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)
        mock_task = Task(**MINIMAL_TASK, history=[])
        events: list[Any] = [
            TaskArtifactUpdateEvent(
                taskId='task_123',
                contextId='session-xyz',
                artifact=Artifact(
                    artifactId='11', parts=[Part(TextPart(text='text'))]
                ),
            ),
            TaskStatusUpdateEvent(
                taskId='task_123',
                contextId='session-xyz',
                status=TaskStatus(state=TaskState.completed),
                final=True,
            ),
        ]

        async def streaming_coro():
            for event in events:
                yield event

        with patch(
            'a2a.server.request_handlers.default_request_handler.EventConsumer.consume_all',
            return_value=streaming_coro(),
        ):
            mock_task_store.get.return_value = mock_task
            mock_queue_manager.tap.return_value = EventQueue()
            request = TaskResubscriptionRequest(
                id='1', params=TaskIdParams(id=mock_task.id)
            )
            response = handler.on_resubscribe_to_task(request)
            assert isinstance(response, AsyncGenerator)
            collected_events: list[Any] = []
            async for event in response:
                collected_events.append(event)
            assert len(collected_events) == len(events)
            assert mock_task.history is not None and len(mock_task.history) == 0

    async def test_on_resubscribe_no_existing_task_error(self) -> None:
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)
        mock_task_store.get.return_value = None
        request = TaskResubscriptionRequest(
            id='1', params=TaskIdParams(id='nonexistent_id')
        )
        response = handler.on_resubscribe_to_task(request)
        assert isinstance(response, AsyncGenerator)
        collected_events: list[Any] = []
        async for event in response:
            collected_events.append(event)
        assert len(collected_events) == 1
        self.assertIsInstance(collected_events[0].root, JSONRPCErrorResponse)
        assert collected_events[0].root.error == TaskNotFoundError()

    async def test_streaming_not_supported_error(
        self,
    ) -> None:
        """Test that on_message_send_stream raises an error when streaming not supported."""
        # Arrange
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )
        # Create agent card with streaming capability disabled
        self.mock_agent_card.capabilities = AgentCapabilities(streaming=False)
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)

        # Act & Assert
        request = SendStreamingMessageRequest(
            id='1',
            params=MessageSendParams(message=Message(**MESSAGE_PAYLOAD)),
        )

        # Should raise ServerError about streaming not supported
        with self.assertRaises(ServerError) as context:
            async for _ in handler.on_message_send_stream(request):
                pass

        self.assertEqual(
            str(context.exception.error.message),
            'Streaming is not supported by the agent',
        )

    async def test_push_notifications_not_supported_error(self) -> None:
        """Test that set_push_notification raises an error when push notifications not supported."""
        # Arrange
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )
        # Create agent card with push notifications capability disabled
        self.mock_agent_card.capabilities = AgentCapabilities(
            pushNotifications=False, streaming=True
        )
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)

        # Act & Assert
        task_push_config = TaskPushNotificationConfig(
            taskId='task_123',
            pushNotificationConfig=PushNotificationConfig(
                url='http://example.com'
            ),
        )
        request = SetTaskPushNotificationConfigRequest(
            id='1', params=task_push_config
        )

        # Should raise ServerError about push notifications not supported
        with self.assertRaises(ServerError) as context:
            await handler.set_push_notification(request)

        self.assertEqual(
            str(context.exception.error.message),
            'Push notifications are not supported by the agent',
        )

    async def test_on_get_push_notification_no_push_notifier(self) -> None:
        """Test get_push_notification with no push notifier configured."""
        # Arrange
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        # Create request handler without a push notifier
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )
        self.mock_agent_card.capabilities = AgentCapabilities(
            pushNotifications=True
        )
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)

        mock_task = Task(**MINIMAL_TASK)
        mock_task_store.get.return_value = mock_task

        # Act
        get_request = GetTaskPushNotificationConfigRequest(
            id='1', params=TaskIdParams(id=mock_task.id)
        )
        response = await handler.get_push_notification(get_request)

        # Assert
        self.assertIsInstance(response.root, JSONRPCErrorResponse)
        self.assertEqual(response.root.error, UnsupportedOperationError())  # type: ignore

    async def test_on_set_push_notification_no_push_notifier(self) -> None:
        """Test set_push_notification with no push notifier configured."""
        # Arrange
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        # Create request handler without a push notifier
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )
        self.mock_agent_card.capabilities = AgentCapabilities(
            pushNotifications=True
        )
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)

        mock_task = Task(**MINIMAL_TASK)
        mock_task_store.get.return_value = mock_task

        # Act
        task_push_config = TaskPushNotificationConfig(
            taskId=mock_task.id,
            pushNotificationConfig=PushNotificationConfig(
                url='http://example.com'
            ),
        )
        request = SetTaskPushNotificationConfigRequest(
            id='1', params=task_push_config
        )
        response = await handler.set_push_notification(request)

        # Assert
        self.assertIsInstance(response.root, JSONRPCErrorResponse)
        self.assertEqual(response.root.error, UnsupportedOperationError())  # type: ignore

    async def test_on_message_send_internal_error(self) -> None:
        """Test on_message_send with an internal error."""
        # Arrange
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)

        # Make the request handler raise an Internal error without specifying an error type
        async def raise_server_error(*args, **kwargs) -> NoReturn:
            raise ServerError(InternalError(message='Internal Error'))

        # Patch the method to raise an error
        with patch.object(
            request_handler, 'on_message_send', side_effect=raise_server_error
        ):
            # Act
            request = SendMessageRequest(
                id='1',
                params=MessageSendParams(message=Message(**MESSAGE_PAYLOAD)),
            )
            response = await handler.on_message_send(request)

            # Assert
            self.assertIsInstance(response.root, JSONRPCErrorResponse)
            self.assertIsInstance(response.root.error, InternalError)  # type: ignore

    async def test_on_message_stream_internal_error(self) -> None:
        """Test on_message_send_stream with an internal error."""
        # Arrange
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )
        self.mock_agent_card.capabilities = AgentCapabilities(streaming=True)
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)

        # Make the request handler raise an Internal error without specifying an error type
        async def raise_server_error(*args, **kwargs):
            raise ServerError(InternalError(message='Internal Error'))
            yield  # Need this to make it an async generator

        # Patch the method to raise an error
        with patch.object(
            request_handler,
            'on_message_send_stream',
            return_value=raise_server_error(),
        ):
            # Act
            request = SendStreamingMessageRequest(
                id='1',
                params=MessageSendParams(message=Message(**MESSAGE_PAYLOAD)),
            )

            # Get the single error response
            responses = []
            async for response in handler.on_message_send_stream(request):
                responses.append(response)

            # Assert
            self.assertEqual(len(responses), 1)
            self.assertIsInstance(responses[0].root, JSONRPCErrorResponse)
            self.assertIsInstance(responses[0].root.error, InternalError)

    async def test_default_request_handler_with_custom_components(self) -> None:
        """Test DefaultRequestHandler initialization with custom components."""
        # Arrange
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        mock_queue_manager = AsyncMock(spec=QueueManager)
        mock_push_notifier = AsyncMock(spec=PushNotifier)
        mock_request_context_builder = AsyncMock(spec=RequestContextBuilder)

        # Act
        handler = DefaultRequestHandler(
            agent_executor=mock_agent_executor,
            task_store=mock_task_store,
            queue_manager=mock_queue_manager,
            push_notifier=mock_push_notifier,
            request_context_builder=mock_request_context_builder,
        )

        # Assert
        self.assertEqual(handler.agent_executor, mock_agent_executor)
        self.assertEqual(handler.task_store, mock_task_store)
        self.assertEqual(handler._queue_manager, mock_queue_manager)
        self.assertEqual(handler._push_notifier, mock_push_notifier)
        self.assertEqual(
            handler._request_context_builder, mock_request_context_builder
        )

    async def test_on_message_send_error_handling(self) -> None:
        """Test error handling in on_message_send when consuming raises ServerError."""
        # Arrange
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)

        # Let task exist
        mock_task = Task(**MINIMAL_TASK)
        mock_task_store.get.return_value = mock_task

        # Set up consume_and_break_on_interrupt to raise ServerError
        async def consume_raises_error(*args, **kwargs) -> NoReturn:
            raise ServerError(error=UnsupportedOperationError())

        with patch(
            'a2a.server.tasks.result_aggregator.ResultAggregator.consume_and_break_on_interrupt',
            side_effect=consume_raises_error,
        ):
            # Act
            request = SendMessageRequest(
                id='1',
                params=MessageSendParams(
                    message=Message(
                        **MESSAGE_PAYLOAD,
                        taskId=mock_task.id,
                        contextId=mock_task.contextId,
                    )
                ),
            )

            response = await handler.on_message_send(request)

            # Assert
            self.assertIsInstance(response.root, JSONRPCErrorResponse)
            self.assertEqual(response.root.error, UnsupportedOperationError())

    async def test_on_message_send_task_id_mismatch(self) -> None:
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)
        mock_task = Task(**MINIMAL_TASK)
        mock_task_store.get.return_value = mock_task
        mock_agent_executor.execute.return_value = None

        async def streaming_coro():
            yield mock_task

        with patch(
            'a2a.server.request_handlers.default_request_handler.EventConsumer.consume_all',
            return_value=streaming_coro(),
        ):
            request = SendMessageRequest(
                id='1',
                params=MessageSendParams(message=Message(**MESSAGE_PAYLOAD)),
            )
            response = await handler.on_message_send(request)
            assert mock_agent_executor.execute.call_count == 1
            self.assertIsInstance(response.root, JSONRPCErrorResponse)
            self.assertIsInstance(response.root.error, InternalError)  # type: ignore

    async def test_on_message_stream_task_id_mismatch(self) -> None:
        mock_agent_executor = AsyncMock(spec=AgentExecutor)
        mock_task_store = AsyncMock(spec=TaskStore)
        request_handler = DefaultRequestHandler(
            mock_agent_executor, mock_task_store
        )

        self.mock_agent_card.capabilities = AgentCapabilities(streaming=True)
        handler = JSONRPCHandler(self.mock_agent_card, request_handler)
        events: list[Any] = [Task(**MINIMAL_TASK)]

        async def streaming_coro():
            for event in events:
                yield event

        with patch(
            'a2a.server.request_handlers.default_request_handler.EventConsumer.consume_all',
            return_value=streaming_coro(),
        ):
            mock_task_store.get.return_value = None
            mock_agent_executor.execute.return_value = None
            request = SendStreamingMessageRequest(
                id='1',
                params=MessageSendParams(message=Message(**MESSAGE_PAYLOAD)),
            )
            response = handler.on_message_send_stream(request)
            assert isinstance(response, AsyncGenerator)
            collected_events: list[Any] = []
            async for event in response:
                collected_events.append(event)
            assert len(collected_events) == 1
            self.assertIsInstance(
                collected_events[0].root, JSONRPCErrorResponse
            )
            self.assertIsInstance(collected_events[0].root.error, InternalError)
